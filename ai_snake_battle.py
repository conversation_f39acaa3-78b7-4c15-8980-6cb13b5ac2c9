import pygame
import random
import math
from enum import Enum

# تهيئة pygame
pygame.init()

# الألوان
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
PURPLE = (128, 0, 128)
ORANGE = (255, 165, 0)

# إعدادات اللعبة
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600
GRID_SIZE = 20
GRID_WIDTH = WINDOW_WIDTH // GRID_SIZE
GRID_HEIGHT = WINDOW_HEIGHT // GRID_SIZE

class Direction(Enum):
    UP = (0, -1)
    DOWN = (0, 1)
    LEFT = (-1, 0)
    RIGHT = (1, 0)

class AISnake:
    def __init__(self, x, y, color, name):
        self.body = [(x, y)]
        self.direction = Direction.RIGHT
        self.color = color
        self.name = name
        self.score = 0
        self.alive = True
        
    def move(self):
        if not self.alive:
            return
            
        head_x, head_y = self.body[0]
        dx, dy = self.direction.value
        new_head = (head_x + dx, head_y + dy)
        
        # التحقق من الحدود
        if (new_head[0] < 0 or new_head[0] >= GRID_WIDTH or 
            new_head[1] < 0 or new_head[1] >= GRID_HEIGHT):
            self.alive = False
            return
            
        # التحقق من التصادم مع الجسم
        if new_head in self.body:
            self.alive = False
            return
            
        self.body.insert(0, new_head)
        
    def grow(self):
        # لا نحذف الذيل عند الأكل
        self.score += 1
        
    def shrink(self):
        if len(self.body) > 1:
            self.body.pop()
    
    def get_head(self):
        return self.body[0] if self.body else None
    
    def check_collision_with_snake(self, other_snake):
        """التحقق من التصادم مع ثعبان آخر"""
        if not self.alive or not other_snake.alive:
            return False
        return self.get_head() in other_snake.body
    
    def get_safe_directions(self, food_pos, other_snake):
        """الحصول على الاتجاهات الآمنة"""
        head_x, head_y = self.body[0]
        safe_directions = []
        
        for direction in Direction:
            dx, dy = direction.value
            new_pos = (head_x + dx, head_y + dy)
            
            # التحقق من الحدود
            if (new_pos[0] < 0 or new_pos[0] >= GRID_WIDTH or 
                new_pos[1] < 0 or new_pos[1] >= GRID_HEIGHT):
                continue
                
            # التحقق من التصادم مع الجسم
            if new_pos in self.body:
                continue
                
            # التحقق من التصادم مع الثعبان الآخر
            if other_snake.alive and new_pos in other_snake.body:
                continue
                
            safe_directions.append(direction)
            
        return safe_directions
    
    def calculate_distance(self, pos1, pos2):
        """حساب المسافة بين نقطتين"""
        return math.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)
    
    def ai_decide_direction(self, food_pos, other_snake):
        """الذكاء الاصطناعي لاتخاذ القرار"""
        if not self.alive:
            return
            
        safe_directions = self.get_safe_directions(food_pos, other_snake)
        
        if not safe_directions:
            # لا توجد اتجاهات آمنة، الثعبان سيموت
            return
            
        head_x, head_y = self.body[0]
        best_direction = safe_directions[0]
        best_score = float('-inf')
        
        for direction in safe_directions:
            dx, dy = direction.value
            new_pos = (head_x + dx, head_y + dy)
            
            # حساب النقاط بناءً على عدة عوامل
            score = 0
            
            # المسافة إلى الطعام (كلما قل كان أفضل)
            food_distance = self.calculate_distance(new_pos, food_pos)
            score -= food_distance * 2
            
            # تجنب الزوايا والمناطق الضيقة
            corners_penalty = 0
            if new_pos[0] <= 2 or new_pos[0] >= GRID_WIDTH - 3:
                corners_penalty += 5
            if new_pos[1] <= 2 or new_pos[1] >= GRID_HEIGHT - 3:
                corners_penalty += 5
            score -= corners_penalty
            
            # تجنب الاقتراب من الثعبان الآخر
            if other_snake.alive:
                other_head = other_snake.get_head()
                if other_head:
                    other_distance = self.calculate_distance(new_pos, other_head)
                    if other_distance < 3:
                        score -= (3 - other_distance) * 10
            
            # تفضيل الاتجاه الحالي (لتجنب التذبذب)
            if direction == self.direction:
                score += 1
                
            if score > best_score:
                best_score = score
                best_direction = direction
                
        self.direction = best_direction

class Game:
    def __init__(self):
        self.screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption("معركة الثعابين الذكية")
        self.clock = pygame.time.Clock()
        
        # إنشاء الثعابين
        self.snake1 = AISnake(5, GRID_HEIGHT // 2, RED, "الثعبان الأحمر")
        self.snake2 = AISnake(GRID_WIDTH - 6, GRID_HEIGHT // 2, BLUE, "الثعبان الأزرق")
        self.snake2.direction = Direction.LEFT
        
        # الطعام
        self.food_pos = self.generate_food()
        
        # الخط
        self.font = pygame.font.Font(None, 36)
        
    def generate_food(self):
        """توليد طعام في موقع عشوائي"""
        while True:
            x = random.randint(0, GRID_WIDTH - 1)
            y = random.randint(0, GRID_HEIGHT - 1)
            pos = (x, y)
            
            # التأكد من أن الطعام ليس على أي ثعبان
            if (pos not in self.snake1.body and pos not in self.snake2.body):
                return pos
    
    def draw_snake(self, snake):
        """رسم الثعبان"""
        if not snake.alive:
            return
            
        for i, segment in enumerate(snake.body):
            x, y = segment
            rect = pygame.Rect(x * GRID_SIZE, y * GRID_SIZE, GRID_SIZE, GRID_SIZE)
            
            if i == 0:  # الرأس
                pygame.draw.rect(self.screen, snake.color, rect)
                pygame.draw.rect(self.screen, WHITE, rect, 2)
            else:  # الجسم
                color = tuple(max(0, c - 30) for c in snake.color)  # لون أغمق للجسم
                pygame.draw.rect(self.screen, color, rect)
                pygame.draw.rect(self.screen, WHITE, rect, 1)
    
    def draw_food(self):
        """رسم الطعام"""
        x, y = self.food_pos
        rect = pygame.Rect(x * GRID_SIZE, y * GRID_SIZE, GRID_SIZE, GRID_SIZE)
        pygame.draw.rect(self.screen, GREEN, rect)
        pygame.draw.rect(self.screen, WHITE, rect, 2)
    
    def draw_ui(self):
        """رسم واجهة المستخدم"""
        # النقاط
        score1_text = self.font.render(f"{self.snake1.name}: {self.snake1.score}", True, WHITE)
        score2_text = self.font.render(f"{self.snake2.name}: {self.snake2.score}", True, WHITE)
        
        self.screen.blit(score1_text, (10, 10))
        self.screen.blit(score2_text, (10, 50))
        
        # حالة الثعابين
        if not self.snake1.alive:
            dead1_text = self.font.render(f"{self.snake1.name} مات!", True, RED)
            self.screen.blit(dead1_text, (10, 90))
            
        if not self.snake2.alive:
            dead2_text = self.font.render(f"{self.snake2.name} مات!", True, RED)
            self.screen.blit(dead2_text, (10, 130))
    
    def check_food_collision(self):
        """التحقق من أكل الطعام"""
        if self.snake1.alive and self.snake1.get_head() == self.food_pos:
            self.snake1.grow()
            self.food_pos = self.generate_food()
            
        elif self.snake2.alive and self.snake2.get_head() == self.food_pos:
            self.snake2.grow()
            self.food_pos = self.generate_food()
    
    def check_collisions(self):
        """التحقق من التصادمات"""
        # التصادم بين الثعابين
        if (self.snake1.alive and self.snake2.alive and 
            self.snake1.get_head() == self.snake2.get_head()):
            # تصادم الرؤوس - كلاهما يموت
            self.snake1.alive = False
            self.snake2.alive = False
            
        elif self.snake1.check_collision_with_snake(self.snake2):
            self.snake1.alive = False
            
        elif self.snake2.check_collision_with_snake(self.snake1):
            self.snake2.alive = False
    
    def game_over(self):
        """التحقق من انتهاء اللعبة"""
        return not self.snake1.alive and not self.snake2.alive
    
    def get_winner(self):
        """تحديد الفائز"""
        if self.snake1.alive and not self.snake2.alive:
            return self.snake1
        elif self.snake2.alive and not self.snake1.alive:
            return self.snake2
        elif self.snake1.score > self.snake2.score:
            return self.snake1
        elif self.snake2.score > self.snake1.score:
            return self.snake2
        else:
            return None  # تعادل
    
    def run(self):
        """تشغيل اللعبة"""
        running = True
        game_over_timer = 0
        
        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_r and self.game_over():
                        # إعادة تشغيل اللعبة
                        self.__init__()
                        game_over_timer = 0
            
            if not self.game_over():
                # تحديث الذكاء الاصطناعي
                self.snake1.ai_decide_direction(self.food_pos, self.snake2)
                self.snake2.ai_decide_direction(self.food_pos, self.snake1)
                
                # تحريك الثعابين
                self.snake1.move()
                self.snake2.move()
                
                # تقليص الثعابين (إزالة الذيل)
                self.snake1.shrink()
                self.snake2.shrink()
                
                # التحقق من التصادمات
                self.check_collisions()
                
                # التحقق من أكل الطعام
                self.check_food_collision()
            else:
                game_over_timer += 1
            
            # الرسم
            self.screen.fill(BLACK)
            
            # رسم الشبكة
            for x in range(0, WINDOW_WIDTH, GRID_SIZE):
                pygame.draw.line(self.screen, (30, 30, 30), (x, 0), (x, WINDOW_HEIGHT))
            for y in range(0, WINDOW_HEIGHT, GRID_SIZE):
                pygame.draw.line(self.screen, (30, 30, 30), (0, y), (WINDOW_WIDTH, y))
            
            self.draw_food()
            self.draw_snake(self.snake1)
            self.draw_snake(self.snake2)
            self.draw_ui()
            
            # رسالة انتهاء اللعبة
            if self.game_over():
                winner = self.get_winner()
                if winner:
                    winner_text = self.font.render(f"الفائز: {winner.name}!", True, YELLOW)
                else:
                    winner_text = self.font.render("تعادل!", True, YELLOW)
                
                text_rect = winner_text.get_rect(center=(WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2))
                self.screen.blit(winner_text, text_rect)
                
                restart_text = self.font.render("اضغط R لإعادة التشغيل", True, WHITE)
                restart_rect = restart_text.get_rect(center=(WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2 + 40))
                self.screen.blit(restart_text, restart_rect)
            
            pygame.display.flip()
            self.clock.tick(10)  # سرعة اللعبة
        
        pygame.quit()

if __name__ == "__main__":
    game = Game()
    game.run()
