import pygame
import random
import time
import math
from enum import Enum
from collections import deque

# Initialize Pygame
pygame.init()

# Set up the game window
WINDOW_SIZE = 800
GRID_SIZE = 20
GRID_COUNT = WINDOW_SIZE // GRID_SIZE

# Enhanced Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 50, 50)
GREEN = (50, 255, 50)
BLUE = (50, 50, 255)
YELLOW = (255, 255, 50)
PURPLE = (255, 50, 255)
ORANGE = (255, 165, 0)
GRAY = (128, 128, 128)
DARK_GREEN = (0, 150, 0)
DARK_RED = (150, 0, 0)

# AI Difficulty Levels
class Difficulty(Enum):
    EASY = 1
    MEDIUM = 2
    HARD = 3
    EXPERT = 4

# Initialize the screen
screen = pygame.display.set_mode((WINDOW_SIZE, WINDOW_SIZE))
pygame.display.set_caption("Auto Snake Game")
clock = pygame.time.Clock()

class Snake:
    def __init__(self, difficulty=Difficulty.MEDIUM):
        self.length = 1
        self.positions = [(GRID_COUNT // 2, GRID_COUNT // 2)]
        self.direction = random.choice([(0, 1), (0, -1), (1, 0), (-1, 0)])
        # Different colors based on difficulty
        if difficulty == Difficulty.EASY:
            self.color = BLUE
        elif difficulty == Difficulty.MEDIUM:
            self.color = GREEN
        elif difficulty == Difficulty.HARD:
            self.color = ORANGE
        else:  # EXPERT
            self.color = PURPLE
        self.score = 0
        self.difficulty = difficulty
        self.moves_without_food = 0
        self.max_moves_without_food = GRID_COUNT * GRID_COUNT

    def get_head_position(self):
        return self.positions[0]

    def is_safe_move(self, x, y):
        """Check if a position is safe to move to"""
        return (0 <= x < GRID_COUNT and 
                0 <= y < GRID_COUNT and 
                (x, y) not in self.positions[:-1])
    
    def get_safe_moves(self, head_x, head_y):
        """Get all safe moves from current position"""
        directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]
        safe_moves = []
        
        for dx, dy in directions:
            new_x, new_y = head_x + dx, head_y + dy
            if self.is_safe_move(new_x, new_y):
                safe_moves.append((dx, dy))
        
        return safe_moves
    
    def calculate_space_around(self, x, y, depth=3):
        """Calculate available space around a position using flood fill"""
        visited = set()
        queue = deque([(x, y)])
        space_count = 0
        
        while queue and space_count < depth * depth:
            curr_x, curr_y = queue.popleft()
            if (curr_x, curr_y) in visited:
                continue
                
            if not self.is_safe_move(curr_x, curr_y):
                continue
                
            visited.add((curr_x, curr_y))
            space_count += 1
            
            # Add neighbors
            for dx, dy in [(0, 1), (0, -1), (1, 0), (-1, 0)]:
                next_x, next_y = curr_x + dx, curr_y + dy
                if (next_x, next_y) not in visited:
                    queue.append((next_x, next_y))
        
        return space_count
    
    def a_star_pathfind(self, start, goal):
        """A* pathfinding algorithm to find optimal path to food"""
        def heuristic(a, b):
            return abs(a[0] - b[0]) + abs(a[1] - b[1])
        
        open_set = [(0, start)]
        came_from = {}
        g_score = {start: 0}
        f_score = {start: heuristic(start, goal)}
        
        while open_set:
            current = min(open_set, key=lambda x: x[0])[1]
            open_set = [item for item in open_set if item[1] != current]
            
            if current == goal:
                # Reconstruct path
                path = []
                while current in came_from:
                    path.append(current)
                    current = came_from[current]
                if len(path) > 1:
                    next_pos = path[-2]
                    return (next_pos[0] - start[0], next_pos[1] - start[1])
                return None
            
            for dx, dy in [(0, 1), (0, -1), (1, 0), (-1, 0)]:
                neighbor = (current[0] + dx, current[1] + dy)
                
                if not self.is_safe_move(neighbor[0], neighbor[1]):
                    continue
                
                tentative_g_score = g_score[current] + 1
                
                if neighbor not in g_score or tentative_g_score < g_score[neighbor]:
                    came_from[neighbor] = current
                    g_score[neighbor] = tentative_g_score
                    f_score[neighbor] = g_score[neighbor] + heuristic(neighbor, goal)
                    
                    if (f_score[neighbor], neighbor) not in open_set:
                        open_set.append((f_score[neighbor], neighbor))
        
        return None
    
    def auto_move(self, food_position):
        current = self.get_head_position()
        head_x, head_y = current
        food_x, food_y = food_position
        
        self.moves_without_food += 1
        
        # Get safe moves
        safe_moves = self.get_safe_moves(head_x, head_y)
        
        if not safe_moves:
            return False
        
        # Choose strategy based on difficulty
        if self.difficulty == Difficulty.EASY:
            # Simple random movement with basic food seeking
            if random.random() < 0.7:  # 70% chance to move toward food
                distances = []
                for dx, dy in safe_moves:
                    new_x, new_y = head_x + dx, head_y + dy
                    distance = abs(new_x - food_x) + abs(new_y - food_y)
                    distances.append(distance)
                best_move = safe_moves[distances.index(min(distances))]
            else:
                best_move = random.choice(safe_moves)
                
        elif self.difficulty == Difficulty.MEDIUM:
            # Improved pathfinding with some space awareness
            best_move = None
            best_score = float('-inf')
            
            for dx, dy in safe_moves:
                new_x, new_y = head_x + dx, head_y + dy
                
                # Distance to food (negative because we want to minimize)
                food_distance = -(abs(new_x - food_x) + abs(new_y - food_y))
                
                # Available space around the new position
                space_score = self.calculate_space_around(new_x, new_y, 2)
                
                # Combined score
                total_score = food_distance * 2 + space_score
                
                if total_score > best_score:
                    best_score = total_score
                    best_move = (dx, dy)
                    
        elif self.difficulty == Difficulty.HARD:
            # A* pathfinding with space consideration
            optimal_direction = self.a_star_pathfind((head_x, head_y), (food_x, food_y))
            
            if optimal_direction and optimal_direction in safe_moves:
                best_move = optimal_direction
            else:
                # Fallback to space-aware movement
                best_move = None
                best_space = 0
                
                for dx, dy in safe_moves:
                    new_x, new_y = head_x + dx, head_y + dy
                    space = self.calculate_space_around(new_x, new_y, 3)
                    
                    if space > best_space:
                        best_space = space
                        best_move = (dx, dy)
                        
        else:  # EXPERT
            # Advanced AI with multiple strategies
            if self.moves_without_food > self.max_moves_without_food // 2:
                # Aggressive food seeking when hungry
                optimal_direction = self.a_star_pathfind((head_x, head_y), (food_x, food_y))
                if optimal_direction and optimal_direction in safe_moves:
                    best_move = optimal_direction
                else:
                    best_move = safe_moves[0]
            else:
                # Balanced approach
                best_move = None
                best_score = float('-inf')
                
                for dx, dy in safe_moves:
                    new_x, new_y = head_x + dx, head_y + dy
                    
                    # Multiple factors
                    food_distance = -(abs(new_x - food_x) + abs(new_y - food_y))
                    space_score = self.calculate_space_around(new_x, new_y, 4)
                    
                    # Avoid walls and corners
                    wall_penalty = 0
                    if new_x == 0 or new_x == GRID_COUNT-1: wall_penalty -= 2
                    if new_y == 0 or new_y == GRID_COUNT-1: wall_penalty -= 2
                    
                    # Prefer moves that don't trap us
                    future_moves = self.get_safe_moves(new_x, new_y)
                    mobility_score = len(future_moves)
                    
                    total_score = food_distance * 3 + space_score * 2 + wall_penalty + mobility_score
                    
                    if total_score > best_score:
                        best_score = total_score
                        best_move = (dx, dy)
        
        if not best_move:
            best_move = safe_moves[0]
            
        self.direction = best_move
        
        new_head = (head_x + self.direction[0], head_y + self.direction[1])
        
        # Check if we've hit the walls or ourselves
        if (not 0 <= new_head[0] < GRID_COUNT or
            not 0 <= new_head[1] < GRID_COUNT or
            new_head in self.positions):
            return False
            
        self.positions.insert(0, new_head)
        if len(self.positions) > self.length:
            self.positions.pop()
            
        return True

    def reset(self):
        self.length = 1
        self.positions = [(GRID_COUNT // 2, GRID_COUNT // 2)]
        self.direction = random.choice([(0, 1), (0, -1), (1, 0), (-1, 0)])
        self.score = 0
        self.moves_without_food = 0

class Food:
    def __init__(self):
        self.position = (0, 0)
        self.color = RED
        self.special_food = False
        self.special_timer = 0
        self.randomize_position()

    def randomize_position(self, snake_positions=None):
        if snake_positions is None:
            snake_positions = []
            
        # Ensure food doesn't spawn on snake
        while True:
            self.position = (random.randint(0, GRID_COUNT-1), 
                           random.randint(0, GRID_COUNT-1))
            if self.position not in snake_positions:
                break
        
        # 10% chance for special food
        self.special_food = random.random() < 0.1
        if self.special_food:
            self.color = YELLOW
            self.special_timer = 100  # Special food lasts for 100 frames
        else:
            self.color = RED
            self.special_timer = 0
    
    def update(self):
        if self.special_food:
            self.special_timer -= 1
            if self.special_timer <= 0:
                self.special_food = False
                self.color = RED

def draw_menu(screen, font, selected_difficulty):
    """Draw the difficulty selection menu"""
    screen.fill(BLACK)
    
    title = font.render('🐍 Enhanced Auto Snake Game 🐍', True, WHITE)
    title_rect = title.get_rect(center=(WINDOW_SIZE//2, 100))
    screen.blit(title, title_rect)
    
    subtitle = pygame.font.Font(None, 24).render('Select AI Difficulty:', True, WHITE)
    subtitle_rect = subtitle.get_rect(center=(WINDOW_SIZE//2, 200))
    screen.blit(subtitle, subtitle_rect)
    
    difficulties = [
        ("1 - Easy (Random + Basic)", Difficulty.EASY),
        ("2 - Medium (Smart Pathfinding)", Difficulty.MEDIUM),
        ("3 - Hard (A* Algorithm)", Difficulty.HARD),
        ("4 - Expert (Advanced AI)", Difficulty.EXPERT)
    ]
    
    for i, (text, diff) in enumerate(difficulties):
        color = YELLOW if diff == selected_difficulty else WHITE
        option = pygame.font.Font(None, 28).render(text, True, color)
        option_rect = option.get_rect(center=(WINDOW_SIZE//2, 280 + i*40))
        screen.blit(option, option_rect)
    
    controls = [
        "Controls:",
        "SPACE - Increase Speed",
        "BACKSPACE - Decrease Speed", 
        "R - Reset Game",
        "M - Return to Menu",
        "ESC - Exit"
    ]
    
    for i, text in enumerate(controls):
        color = GRAY if i == 0 else WHITE
        control = pygame.font.Font(None, 20).render(text, True, color)
        control_rect = control.get_rect(center=(WINDOW_SIZE//2, 480 + i*25))
        screen.blit(control, control_rect)
    
    start_text = pygame.font.Font(None, 32).render('Press ENTER to Start', True, GREEN)
    start_rect = start_text.get_rect(center=(WINDOW_SIZE//2, 650))
    screen.blit(start_text, start_rect)

def draw_game(screen, snake, food, game_speed, font):
    """Draw the game state"""
    screen.fill(BLACK)
    
    # Draw grid lines
    for x in range(0, WINDOW_SIZE, GRID_SIZE):
        pygame.draw.line(screen, GRAY, (x, 0), (x, WINDOW_SIZE))
    for y in range(0, WINDOW_SIZE, GRID_SIZE):
        pygame.draw.line(screen, GRAY, (0, y), (WINDOW_SIZE, y))
    
    # Draw snake with gradient effect
    for i, position in enumerate(snake.positions):
        # Head is brightest, tail is darkest
        intensity = 1.0 - (i / len(snake.positions)) * 0.5
        color = (int(snake.color[0] * intensity), 
                int(snake.color[1] * intensity), 
                int(snake.color[2] * intensity))
        
        rect = pygame.Rect(position[0]*GRID_SIZE + 1, position[1]*GRID_SIZE + 1, 
                         GRID_SIZE-2, GRID_SIZE-2)
        pygame.draw.rect(screen, color, rect)
        
        # Draw eyes on head
        if i == 0:
            eye_size = 3
            eye1_pos = (position[0]*GRID_SIZE + 5, position[1]*GRID_SIZE + 5)
            eye2_pos = (position[0]*GRID_SIZE + GRID_SIZE-8, position[1]*GRID_SIZE + 5)
            pygame.draw.circle(screen, BLACK, eye1_pos, eye_size)
            pygame.draw.circle(screen, BLACK, eye2_pos, eye_size)
    
    # Draw food with pulsing effect
    pulse = abs(math.sin(pygame.time.get_ticks() * 0.01)) * 0.3 + 0.7
    food_color = (int(food.color[0] * pulse), 
                 int(food.color[1] * pulse), 
                 int(food.color[2] * pulse))
    
    food_rect = pygame.Rect(food.position[0]*GRID_SIZE + 2, food.position[1]*GRID_SIZE + 2, 
                          GRID_SIZE-4, GRID_SIZE-4)
    pygame.draw.rect(screen, food_color, food_rect)
    
    # Special food indicator
    if food.special_food:
        pygame.draw.circle(screen, WHITE, 
                         (food.position[0]*GRID_SIZE + GRID_SIZE//2, 
                          food.position[1]*GRID_SIZE + GRID_SIZE//2), 
                         GRID_SIZE//2 - 1, 2)
    
    # Draw UI
    score_text = font.render(f'Score: {snake.score}', True, WHITE)
    screen.blit(score_text, (10, 10))
    
    speed_text = font.render(f'Speed: {game_speed}', True, WHITE)
    screen.blit(speed_text, (10, 40))
    
    difficulty_text = font.render(f'AI: {snake.difficulty.name}', True, WHITE)
    screen.blit(difficulty_text, (10, 70))
    
    length_text = font.render(f'Length: {snake.length}', True, WHITE)
    screen.blit(length_text, (10, 100))
    
    # High score (simple implementation)
    if hasattr(snake, 'high_score'):
        high_score_text = font.render(f'High Score: {snake.high_score}', True, YELLOW)
        screen.blit(high_score_text, (WINDOW_SIZE - 200, 10))

def main():
    font = pygame.font.Font(None, 36)
    selected_difficulty = Difficulty.MEDIUM
    in_menu = True
    snake = None
    food = None
    game_speed = 10
    high_score = 0
    
    while True:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                return
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    pygame.quit()
                    return
                elif in_menu:
                    if event.key == pygame.K_1:
                        selected_difficulty = Difficulty.EASY
                    elif event.key == pygame.K_2:
                        selected_difficulty = Difficulty.MEDIUM
                    elif event.key == pygame.K_3:
                        selected_difficulty = Difficulty.HARD
                    elif event.key == pygame.K_4:
                        selected_difficulty = Difficulty.EXPERT
                    elif event.key == pygame.K_RETURN:
                        snake = Snake(selected_difficulty)
                        snake.high_score = high_score
                        food = Food()
                        food.randomize_position(snake.positions)
                        game_speed = 10
                        in_menu = False
                else:
                    if event.key == pygame.K_SPACE:
                        game_speed = min(game_speed + 5, 50)
                    elif event.key == pygame.K_BACKSPACE:
                        game_speed = max(game_speed - 5, 5)
                    elif event.key == pygame.K_r:
                        snake.reset()
                        food.randomize_position(snake.positions)
                        game_speed = 10
                    elif event.key == pygame.K_m:
                        in_menu = True
        
        if in_menu:
            draw_menu(screen, font, selected_difficulty)
        else:
            # Update food
            food.update()
            
            # Computer controls the snake
            if not snake.auto_move(food.position):
                print(f"Game Over! Score: {snake.score}, Length: {snake.length}")
                if snake.score > high_score:
                    high_score = snake.score
                    print(f"New High Score: {high_score}!")
                snake.reset()
                food.randomize_position(snake.positions)
                game_speed = 10
                time.sleep(1)
                continue
            
            # Check if snake ate the food
            if snake.get_head_position() == food.position:
                if food.special_food:
                    snake.length += 2  # Special food gives 2 points
                    snake.score += 2
                else:
                    snake.length += 1
                    snake.score += 1
                
                snake.moves_without_food = 0
                food.randomize_position(snake.positions)
            
            draw_game(screen, snake, food, game_speed, font)
        
        pygame.display.update()
        clock.tick(game_speed if not in_menu else 60)

if __name__ == "__main__":
    main()
    pygame.quit()
