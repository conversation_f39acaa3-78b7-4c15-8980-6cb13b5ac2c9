import random
import time
import os
from collections import defaultdict

def print_board(board):
    """Print the game board with improved formatting"""
    os.system('cls' if os.name == 'nt' else 'clear')  # Clear screen
    print("\n" + "=" * 30)
    print("    TIC-TAC-TOE GAME")
    print("=" * 30)
    print("\n   1   2   3")
    for i, row in enumerate(board):
        print(f"{i+1}  {' | '.join(row)}")
        if i < 2:
            print("   ---------")
    print()

def check_winner(board, player):
    # Check rows
    for row in board:
        if all(cell == player for cell in row):
            return True

    # Check columns
    for col in range(3):
        if all(board[row][col] == player for row in range(3)):
            return True

    # Check diagonals
    if all(board[i][i] == player for i in range(3)):
        return True
    if all(board[i][2-i] == player for i in range(3)):
        return True

    return False

def is_board_full(board):
    return all(cell != " " for row in board for cell in row)

def get_available_moves(board):
    """Get all available moves on the board"""
    return [(i, j) for i in range(3) for j in range(3) if board[i][j] == " "]

def minimax(board, depth, is_maximizing, player, opponent):
    """Minimax algorithm for AI decision making"""
    if check_winner(board, player):
        return 10 - depth
    if check_winner(board, opponent):
        return depth - 10
    if is_board_full(board):
        return 0
    
    if is_maximizing:
        max_eval = float('-inf')
        for i, j in get_available_moves(board):
            board[i][j] = player
            eval_score = minimax(board, depth + 1, False, player, opponent)
            board[i][j] = " "
            max_eval = max(max_eval, eval_score)
        return max_eval
    else:
        min_eval = float('inf')
        for i, j in get_available_moves(board):
            board[i][j] = opponent
            eval_score = minimax(board, depth + 1, True, player, opponent)
            board[i][j] = " "
            min_eval = min(min_eval, eval_score)
        return min_eval

def get_computer_move(board, player, difficulty="smart"):
    """Get computer move based on difficulty level"""
    available_moves = get_available_moves(board)
    
    if not available_moves:
        return None
    
    if difficulty == "random":
        return random.choice(available_moves)
    elif difficulty == "smart":
        opponent = "O" if player == "X" else "X"
        
        # Check for winning move
        for i, j in available_moves:
            board[i][j] = player
            if check_winner(board, player):
                board[i][j] = " "
                return (i, j)
            board[i][j] = " "
        
        # Check for blocking move
        for i, j in available_moves:
            board[i][j] = opponent
            if check_winner(board, opponent):
                board[i][j] = " "
                return (i, j)
            board[i][j] = " "
        
        # Use minimax for best move
        best_move = None
        best_value = float('-inf')
        
        for i, j in available_moves:
            board[i][j] = player
            move_value = minimax(board, 0, False, player, opponent)
            board[i][j] = " "
            
            if move_value > best_value:
                best_value = move_value
                best_move = (i, j)
        
        return best_move if best_move else random.choice(available_moves)

def play_game(game_speed=1.5, x_difficulty="smart", o_difficulty="smart"):
    """Play a single game with configurable settings"""
    # Initialize the board
    board = [[" " for _ in range(3)] for _ in range(3)]
    players = ["X", "O"]
    difficulties = [x_difficulty, o_difficulty]
    current_player = 0
    move_count = 0
    
    print_board(board)
    print(f"Game starts! X({x_difficulty}) vs O({o_difficulty})")
    time.sleep(1)
    
    while True:
        print(f"\n🤖 Computer {players[current_player]} ({difficulties[current_player]}) is thinking...")
        time.sleep(game_speed)  # Configurable delay
        
        # Get computer's move
        move = get_computer_move(board, players[current_player], difficulties[current_player])
        if move is None:
            break
            
        row, col = move
        board[row][col] = players[current_player]
        move_count += 1
        
        # Show the move
        print(f"Computer {players[current_player]} places at position ({row+1}, {col+1})")
        print_board(board)
        
        # Check for winner
        if check_winner(board, players[current_player]):
            print(f"\n🎉 Computer {players[current_player]} wins in {move_count} moves!")
            return players[current_player]
            
        # Check for tie
        if is_board_full(board):
            print(f"\n🤝 It's a tie after {move_count} moves!")
            return "Tie"
            
        # Switch players
        current_player = (current_player + 1) % 2
    
    return "Tie"

def get_game_settings():
    """Get game configuration from user"""
    print("\n" + "=" * 50)
    print("         GAME CONFIGURATION")
    print("=" * 50)
    
    # Game speed
    print("\n1. Game Speed:")
    print("   1 - Fast (0.5s)")
    print("   2 - Normal (1.5s)")
    print("   3 - Slow (3s)")
    
    speed_choice = input("Choose speed (1-3, default 2): ").strip()
    speeds = {"1": 0.5, "2": 1.5, "3": 3.0}
    game_speed = speeds.get(speed_choice, 1.5)
    
    # Difficulty levels
    print("\n2. Difficulty Levels:")
    print("   1 - Random (completely random moves)")
    print("   2 - Smart (strategic AI)")
    
    x_diff = input("Choose X difficulty (1-2, default 2): ").strip()
    o_diff = input("Choose O difficulty (1-2, default 2): ").strip()
    
    difficulties = {"1": "random", "2": "smart"}
    x_difficulty = difficulties.get(x_diff, "smart")
    o_difficulty = difficulties.get(o_diff, "smart")
    
    return game_speed, x_difficulty, o_difficulty

def display_statistics(stats):
    """Display game statistics"""
    total_games = sum(stats.values())
    if total_games == 0:
        return
    
    print("\n" + "=" * 40)
    print("           GAME STATISTICS")
    print("=" * 40)
    print(f"Total Games Played: {total_games}")
    print(f"X Wins: {stats['X']} ({stats['X']/total_games*100:.1f}%)")
    print(f"O Wins: {stats['O']} ({stats['O']/total_games*100:.1f}%)")
    print(f"Ties: {stats['Tie']} ({stats['Tie']/total_games*100:.1f}%)")
    print("=" * 40)

if __name__ == "__main__":
    print("\n" + "=" * 60)
    print("    🎮 WELCOME TO ENHANCED TIC-TAC-TOE! 🎮")
    print("         Computer vs Computer Edition")
    print("=" * 60)
    
    # Game statistics
    stats = defaultdict(int)
    
    # Get initial settings
    game_speed, x_difficulty, o_difficulty = get_game_settings()
    
    while True:
        print("\n" + "-" * 50)
        print("Starting new game...")
        print("-" * 50)
        
        # Play game and record result
        result = play_game(game_speed, x_difficulty, o_difficulty)
        stats[result] += 1
        
        # Display statistics
        display_statistics(stats)
        
        # Ask for next action
        print("\nWhat would you like to do?")
        print("1 - Play another game")
        print("2 - Change settings")
        print("3 - Exit")
        
        choice = input("Choose option (1-3): ").strip()
        
        if choice == "2":
            game_speed, x_difficulty, o_difficulty = get_game_settings()
        elif choice == "3":
            break
        elif choice != "1":
            print("Invalid choice, playing another game...")
    
    print("\n" + "=" * 50)
    print("    🎯 FINAL STATISTICS 🎯")
    display_statistics(stats)
    print("\n    Thank you for playing! 👋")
    print("=" * 50)
